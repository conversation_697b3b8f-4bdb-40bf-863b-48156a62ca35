import { useEffect, useRef, useCallback, useState } from 'react';
import { getLocalStorage, setLocalStorage } from '../utils/helper';
import { constants } from '../utils/constants';

/**
 * Custom hook for auto Picture-in-Picture functionality
 * Automatically opens PIP mode when screen changes are detected
 */
export function useAutoPip({
  togglePipMode,
  isSupported,
  isElectronApp = false,
  setToastNotification,
  setToastStatus,
  setShowToast,
}) {
  const [autoPipEnabled, setAutoPipEnabled] = useState(false);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const lastScreenChangeRef = useRef(Date.now());
  const pipActivationTimeoutRef = useRef(null);
  const currentDisplayRef = useRef(null);
  const isInitializedRef = useRef(false);

  // Load auto-PIP settings from localStorage
  useEffect(() => {
    const savedSettings = getLocalStorage(constants.AUTO_PIP_SETTINGS);
    if (savedSettings) {
      setAutoPipEnabled(savedSettings.enabled || false);
      setPermissionGranted(savedSettings.permissionGranted || false);
    }
  }, []);

  // Save auto-PIP settings to localStorage
  const saveAutoPipSettings = useCallback((enabled, permission = permissionGranted) => {
    const settings = {
      enabled,
      permissionGranted: permission,
      lastUpdated: Date.now(),
    };
    setLocalStorage(constants.AUTO_PIP_SETTINGS, settings);
  }, [permissionGranted]);

  // Request screen detection permissions
  const requestPermissions = useCallback(async () => {
    try {
      if (isElectronApp) {
        // For Electron app, we can use screen API directly
        setPermissionGranted(true);
        saveAutoPipSettings(autoPipEnabled, true);
        return true;
      } else {
        // For web, request screen capture permission (needed for screen detection)
        if ('getDisplayMedia' in navigator.mediaDevices) {
          try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
              video: { mediaSource: 'screen' },
              audio: false
            });
            // Stop the stream immediately as we only needed permission
            stream.getTracks().forEach(track => track.stop());
            setPermissionGranted(true);
            saveAutoPipSettings(autoPipEnabled, true);
            return true;
          } catch (error) {
            console.warn('Screen capture permission denied:', error);
            setToastNotification?.('Screen detection permission is required for auto-PIP');
            setToastStatus?.('warning');
            setShowToast?.(true);
            return false;
          }
        } else {
          setToastNotification?.('Screen detection not supported in this browser');
          setToastStatus?.('error');
          setShowToast?.(true);
          return false;
        }
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
      setToastNotification?.('Failed to request screen detection permissions');
      setToastStatus?.('error');
      setShowToast?.(true);
      return false;
    }
  }, [isElectronApp, autoPipEnabled, saveAutoPipSettings, setToastNotification, setToastStatus, setShowToast]);

  // Detect screen changes using various methods
  const detectScreenChange = useCallback(() => {
    const now = Date.now();
    const timeSinceLastChange = now - lastScreenChangeRef.current;
    
    // Debounce screen change detection (minimum 2 seconds between activations)
    if (timeSinceLastChange < 2000) {
      return;
    }

    lastScreenChangeRef.current = now;

    // Clear any existing timeout
    if (pipActivationTimeoutRef.current) {
      clearTimeout(pipActivationTimeoutRef.current);
    }

    // Activate PIP with a small delay to ensure screen change is complete
    pipActivationTimeoutRef.current = setTimeout(async () => {
      if (autoPipEnabled && permissionGranted && isSupported) {
        try {
          const success = await togglePipMode(true);
          if (success) {
            setToastNotification?.('Auto-PIP activated due to screen change');
            setToastStatus?.('success');
            setShowToast?.(true);
          }
        } catch (error) {
          console.error('Failed to activate auto-PIP:', error);
        }
      }
    }, 500);
  }, [autoPipEnabled, permissionGranted, isSupported, togglePipMode, setToastNotification, setToastStatus, setShowToast]);

  // Screen change detection for Electron
  const setupElectronScreenDetection = useCallback(() => {
    if (!isElectronApp || !window.electronAPI) return;

    const handleScreenChange = () => {
      detectScreenChange();
    };

    // Listen for window focus/blur events from Electron
    window.electronAPI.ipcRenderer.on('window-focused', handleScreenChange);
    window.electronAPI.ipcRenderer.on('window-blurred', handleScreenChange);

    return () => {
      window.electronAPI.ipcRenderer.removeListener('window-focused', handleScreenChange);
      window.electronAPI.ipcRenderer.removeListener('window-blurred', handleScreenChange);
    };
  }, [isElectronApp, detectScreenChange]);

  // Screen change detection for web browsers
  const setupWebScreenDetection = useCallback(() => {
    if (isElectronApp) return;

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        detectScreenChange();
      }
    };

    const handleWindowBlur = () => {
      detectScreenChange();
    };

    const handleScreenChange = () => {
      // Check if screen configuration changed
      if (screen.availWidth !== currentDisplayRef.current?.width || 
          screen.availHeight !== currentDisplayRef.current?.height) {
        currentDisplayRef.current = {
          width: screen.availWidth,
          height: screen.availHeight
        };
        detectScreenChange();
      }
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleWindowBlur);
    window.addEventListener('resize', handleScreenChange);

    // Store initial screen dimensions
    currentDisplayRef.current = {
      width: screen.availWidth,
      height: screen.availHeight
    };

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleWindowBlur);
      window.removeEventListener('resize', handleScreenChange);
    };
  }, [isElectronApp, detectScreenChange]);

  // Setup screen detection when auto-PIP is enabled
  useEffect(() => {
    if (!autoPipEnabled || !permissionGranted || !isSupported) {
      return;
    }

    let cleanup;
    
    if (isElectronApp) {
      cleanup = setupElectronScreenDetection();
    } else {
      cleanup = setupWebScreenDetection();
    }

    isInitializedRef.current = true;

    return () => {
      if (cleanup) cleanup();
      if (pipActivationTimeoutRef.current) {
        clearTimeout(pipActivationTimeoutRef.current);
      }
    };
  }, [autoPipEnabled, permissionGranted, isSupported, isElectronApp, setupElectronScreenDetection, setupWebScreenDetection]);

  // Toggle auto-PIP setting
  const toggleAutoPip = useCallback(async (enabled) => {
    if (enabled && !permissionGranted) {
      const granted = await requestPermissions();
      if (!granted) {
        return false;
      }
    }

    setAutoPipEnabled(enabled);
    saveAutoPipSettings(enabled, permissionGranted);
    
    if (enabled) {
      setToastNotification?.('Auto-PIP enabled - PIP will activate when you change screens');
      setToastStatus?.('success');
      setShowToast?.(true);
    } else {
      setToastNotification?.('Auto-PIP disabled');
      setToastStatus?.('info');
      setShowToast?.(true);
    }

    return true;
  }, [permissionGranted, requestPermissions, saveAutoPipSettings, setToastNotification, setToastStatus, setShowToast]);

  return {
    autoPipEnabled,
    permissionGranted,
    toggleAutoPip,
    requestPermissions,
  };
}
